package com.drex.core.service.business.rexy.impl;

import com.alibaba.fastjson2.JSON;
import com.drex.core.api.common.CoreException;
import com.drex.core.api.common.CoreResponseCode;
import com.drex.core.api.common.RexyConstant;
import com.drex.core.api.request.*;
import com.drex.core.api.response.InformationDTO;
import com.drex.core.api.response.MaizeDTO;
import com.drex.core.dal.tablestore.IdUtils;
import com.drex.core.dal.tablestore.builder.MaizeRecordBuilder;
import com.drex.core.dal.tablestore.model.MaizeRecord;
import com.drex.core.model.RexyBusinessCode;
import com.drex.core.service.business.rexy.Base62Encoding;
import com.drex.core.service.business.rexy.InformationService;
import com.drex.core.service.business.rexy.MaizeService;
import com.drex.core.service.business.rexy.RexyBasketService;
import com.drex.core.service.mapperstruct.MaizeMapperStruct;
import com.drex.core.dal.tablestore.builder.VideoViewingSessionBuilder;
import com.drex.core.dal.tablestore.model.VideoViewingSession;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MaizeServiceImpl implements MaizeService {

    @Resource
    private SocialCheckManager socialCheckManager;
    @Resource
    private MaizeRecordBuilder maizeRecordBuilder;
    @Resource
    private MaizeMapperStruct maizeMapperStruct;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private IdUtils idUtils;
    @Resource
    private RexyBasketService rexyBasketService;
    @Resource
    private InformationService informationService;
    @Resource
    private VideoViewingSessionBuilder videoViewingSessionBuilder;

    private static final String COLLECT_MAIZE = "collect_maize";

    @Override
    public MaizeDTO generateMaize(GenerateMaizeRequest request) {
        InformationDTO informationDTO = informationService.getByLinkId(request.getSocialEventBody().getSocialContentId());
//        boolean check = socialCheckManager.check(request, informationDTO);
//        if(!check){
//            return null;
//        }
        String key = String.format("%s:%s:%s", COLLECT_MAIZE, request.getSocialEventBody().getSocialContentId(), request.getCustomerId());
        MaizeDTO cachedMaizeDTO = JSON.parseObject(String.valueOf(redisTemplate.opsForHash().get(key, String.valueOf(request.getProgress() == null ? 1 : request.getProgress()))), MaizeDTO.class);
        if(cachedMaizeDTO == null){
            String code = generateCode(request.getSocialPlatform());
            cachedMaizeDTO = MaizeDTO.builder()
                    .code(code)
                    .score(Long.parseLong(informationDTO.getRewardAmount()))
                    .maizeStatus(RexyConstant.MaizeStatus.ISSUED)
                    .build();
            redisTemplate.opsForHash().put(key, String.valueOf(request.getProgress() == null ? 1 : request.getProgress()), JSON.toJSONString(cachedMaizeDTO));
        }
        MaizeDTO maizeDTO = MaizeDTO.builder()
                .socialPlatform(request.getSocialPlatform())
                .socialEvent(request.getSocialEvent())
                .code(String.valueOf(cachedMaizeDTO.getCode()))
                .level(SocialConstant.MaizeLevelEnum.GOLD.name())
                .score(cachedMaizeDTO.getScore())
                .expireTime(request.getExpireTime())
                .progress(request.getProgress() == null ? 1 : request.getProgress())
                .maizeStatus(RexyConstant.MaizeStatus.ISSUED)
                .build();
        MaizeRecord maizeRecord = maizeMapperStruct.toMaizeRecord(maizeDTO, request);
        if(maizeRecordBuilder.save(maizeRecord)){
            return maizeDTO;
        }
        return cachedMaizeDTO;
    }

    @Override
    public List<MaizeDTO> lastMaize(LastMaizeRequest request) {
        List<MaizeDTO> maizeDTOS = new ArrayList<>();
        //查询相应的资源的奖励
        InformationDTO informationDTO = informationService.getByLinkId(request.getContentId());
        if(informationDTO == null || !informationDTO.getIsReward()){
            return null;
        }
        //构建奖励段
        List<Integer> rewards = buildRewards(informationDTO.getLink(), Integer.parseInt(informationDTO.getRewardAmount()));
        List<SocialConstant.EventEnum> values = Arrays.stream(SocialConstant.EventEnum.values()).filter(event -> event.getPlatform().contains(request.getSocialPlatform())).toList();
        for(SocialConstant.EventEnum eventEnum : values){
            List<MaizeRecord> maizeRecords = maizeRecordBuilder.findMaizeRecords(request.getCustomerId(), eventEnum.name(), request.getContentId());
            Map<Integer, MaizeRecord> maizeRecordsMap = maizeRecords.stream().collect(Collectors.toMap(MaizeRecord::getProgress, Function.identity()));
            for(int i = 1; i <= rewards.size(); i++){
                MaizeRecord maizeRecord = maizeRecordsMap.get(i);
                MaizeDTO.MaizeDTOBuilder maizeDTOBuilder = MaizeDTO.builder()
                        .progress(i)
                        .hidden(i != rewards.size())
                        .score((long)rewards.get(i - 1))
                        .maizeStatus(RexyConstant.MaizeStatus.UNCLAIMED);
                if(maizeRecord != null){
                    maizeDTOBuilder.code(maizeRecord.getMaizeCode());
                    maizeDTOBuilder.maizeStatus(RexyConstant.MaizeStatus.ISSUED);
                    if(RexyConstant.MaizeStatus.CLAIMED.name().equals(maizeRecord.getStatus())){
                        maizeDTOBuilder.maizeStatus(RexyConstant.MaizeStatus.CLAIMED);
                    }else if(maizeRecord.getExpireTime() != null && maizeRecord.getExpireTime() < System.currentTimeMillis()){
                        maizeDTOBuilder.maizeStatus(RexyConstant.MaizeStatus.EXPIRED);
                    }
                }
                MaizeDTO maizeDTO = maizeDTOBuilder.build();
                if(maizeDTO.getCode() == null){
                    String code = generateCode(request.getSocialPlatform());
                    maizeDTO.setCode(code);
                    //放到缓存中
                    String key = String.format("%s:%s:%s", COLLECT_MAIZE, request.getContentId(), request.getCustomerId());
                    redisTemplate.opsForHash().put(key, String.valueOf(maizeDTO.getProgress()), JSON.toJSONString(maizeDTO));
                    redisTemplate.expire(key, 1, TimeUnit.DAYS);
                }
                maizeDTOS.add(maizeDTO);
            }
        }
        return maizeDTOS;
    }

    @Override
    public MaizeDTO collectMaize(CollectMaizeRequest request) throws CoreException {
        //当前的奖励
        MaizeRecord maizeRecord = maizeRecordBuilder.findMaizeRecord(request.getMaizeCode());
        if(maizeRecord == null){
            throw new CoreException(CoreResponseCode.REWARD_NOT_FOUND);
        }
        if(maizeRecord.getExpireTime() != null && maizeRecord.getExpireTime() < System.currentTimeMillis()){
            throw new CoreException(CoreResponseCode.REWARD_EXPIRED);
        }
        if(maizeRecord.getStatus() != null && RexyConstant.MaizeStatus.CLAIMED.name().equals(maizeRecord.getStatus())){
            throw new CoreException(CoreResponseCode.REWARD_ALREADY_CLAIMED);
        }

        // 构建基础奖励DTO
        MaizeDTO maizeDTO = MaizeDTO.builder()
                .socialPlatform(SocialConstant.PlatformEnum.valueOf(maizeRecord.getSocialPlatform()))
                .socialEvent(SocialConstant.EventEnum.valueOf(maizeRecord.getSocialEvent()))
                .code(request.getMaizeCode())
                .level(maizeRecord.getMaizeLevel())
                .score(maizeRecord.getMaizeScore()).build();

        // 发放奖励到用户篮子
        OperateMaizeKernelRequest build = OperateMaizeKernelRequest.builder()
                .customerId(request.getCustomerId())
                .amount(BigDecimal.valueOf(maizeDTO.getScore()))
                .basketType(RexyConstant.RexyBasketsTypeEnum.normal)
                .operateType(RexyConstant.OperateTypeEnum.collect)
                .businessCode(RexyBusinessCode.COLLECT_MAIZE.getCode())
                .businessId(Base62Encoding.decode(request.getMaizeCode()))
                .build();
        rexyBasketService.collectMaizeKernel(build);

        // 更新奖励状态为已领取
        maizeRecordBuilder.updateStatus(maizeRecord.getMaizeCode(), RexyConstant.MaizeStatus.CLAIMED.name());

        // 检查是否需要发放额外奖励（长视频完成所有阶段后的第四段奖励）
        List<MaizeDTO> extraRewards = checkAndGenerateExtraRewards(request.getCustomerId(), maizeRecord);
        if (extraRewards != null && !extraRewards.isEmpty()) {
            maizeDTO.setExtraRewards(extraRewards);
        }

        // 更新VideoViewingSession表的rewardGranted字段
        updateVideoViewingSessionRewardGranted(request.getCustomerId(), maizeRecord.getSocialContentId());

        return maizeDTO;
    }

    private String generateCode(SocialConstant.PlatformEnum socialPlatformEnum) {
        String id = idUtils.nextId();
        if(SocialConstant.PlatformEnum.X == socialPlatformEnum){
            id = "21"+id;
        }else if(SocialConstant.PlatformEnum.YouTube == socialPlatformEnum){
            id = "22"+id;
        }
        String code = Base62Encoding.encode(id);
        log.info("generateCode id:{},{}", id, code);
        return code;
    }

    private List<Integer> buildRewards(String link, Integer rewardAmount) {
        if(link.contains("x.com") || link.contains("twitter.com")){
            return List.of(rewardAmount);
        }else if(link.contains("youtube.com/shorts")){
            return Collections.nCopies(1, rewardAmount);
        }else if(link.contains("youtube.com")){
            return Collections.nCopies(4, rewardAmount/4);
        }
        return null;
    }

    /**
     * 检查并生成额外奖励（长视频完成所有阶段后的第四段奖励）
     */
    private List<MaizeDTO> checkAndGenerateExtraRewards(String customerId, MaizeRecord currentMaizeRecord) {
        try {
            // 获取视频信息
            InformationDTO informationDTO = informationService.getByLinkId(currentMaizeRecord.getSocialContentId());
            if (informationDTO == null) {
                return null;
            }

            // 只有长视频才有额外奖励
            if (!informationDTO.getLink().contains("youtube.com") || informationDTO.getLink().contains("shorts")) {
                return null;
            }

            // 构建奖励段配置
            List<Integer> rewards = buildRewards(informationDTO.getLink(), Integer.parseInt(informationDTO.getRewardAmount()));
            if (rewards == null || rewards.size() < 3) {
                return null; // 长视频应该有3个阶段
            }

            // 查询用户在该视频上的所有奖励记录
            List<MaizeRecord> allMaizeRecords = maizeRecordBuilder.findMaizeRecords(
                    customerId,
                    currentMaizeRecord.getSocialEvent(),
                    currentMaizeRecord.getSocialContentId()
            );

            // 检查是否已领取完前3个阶段的奖励
            Set<Integer> claimedStages = allMaizeRecords.stream()
                    .filter(record -> RexyConstant.MaizeStatus.CLAIMED.name().equals(record.getStatus()))
                    .map(MaizeRecord::getProgress)
                    .collect(Collectors.toSet());

            // 检查是否已领取完前3个阶段（1、2、3）
            boolean allStagesClaimed = claimedStages.contains(1) && claimedStages.contains(2) && claimedStages.contains(3);
            if (!allStagesClaimed) {
                return null;
            }

            // 检查是否已经发放过第四段奖励
            boolean extraRewardAlreadyGranted = allMaizeRecords.stream()
                    .anyMatch(record -> record.getProgress() != null && record.getProgress() == 4);
            if (extraRewardAlreadyGranted) {
                return null;
            }

            // 从缓存获取第四段奖励code
            String key = String.format("%s:%s:%s", COLLECT_MAIZE, currentMaizeRecord.getSocialContentId(), customerId);
            MaizeDTO cachedExtraReward = JSON.parseObject(
                    String.valueOf(redisTemplate.opsForHash().get(key, "4")),
                    MaizeDTO.class
            );

            if (cachedExtraReward == null) {
                // 生成第四段奖励code并缓存
                String extraCode = generateCode(SocialConstant.PlatformEnum.valueOf(currentMaizeRecord.getSocialPlatform()));
                cachedExtraReward = MaizeDTO.builder()
                        .code(extraCode)
                        .score((long) (Integer.parseInt(informationDTO.getRewardAmount()) / 4)) // 第四段奖励金额
                        .build();
                redisTemplate.opsForHash().put(key, "4", JSON.toJSONString(cachedExtraReward));
                redisTemplate.expire(key, 1, TimeUnit.DAYS);
            }

            // 构建额外奖励对象
            MaizeDTO extraReward = MaizeDTO.builder()
                    .code(cachedExtraReward.getCode())
                    .score(cachedExtraReward.getScore())
                    .build();

            return List.of(extraReward);

        } catch (Exception e) {
            log.error("检查额外奖励时发生错误: customerId={}, maizeCode={}",
                    customerId, currentMaizeRecord.getMaizeCode(), e);
            return null;
        }
    }

    /**
     * 更新VideoViewingSession表的rewardGranted字段
     */
    private void updateVideoViewingSessionRewardGranted(String customerId, String videoId) {
        try {
            // 查询用户对该视频的会话记录
            VideoViewingSession session = videoViewingSessionBuilder.hasRewardGranted(customerId, videoId);
            if (session != null && (session.getRewardGranted() == null || !session.getRewardGranted())) {
                // 更新rewardGranted字段为true
                session.setRewardGranted(true);
                session.setModified(System.currentTimeMillis());
                videoViewingSessionBuilder.update(session);
                log.info("更新VideoViewingSession rewardGranted字段: customerId={}, videoId={}", customerId, videoId);
            }
        } catch (Exception e) {
            log.error("更新VideoViewingSession rewardGranted字段时发生错误: customerId={}, videoId={}",
                    customerId, videoId, e);
        }
    }
}
