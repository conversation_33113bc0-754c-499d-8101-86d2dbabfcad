package com.drex.core.service.business.video.impl;

import com.drex.core.api.common.RexyConstant;
import com.drex.core.api.request.SocialConstant;
import com.drex.core.api.response.VideoReportResponse;
import com.drex.core.dal.tablestore.builder.MaizeRecordBuilder;
import com.drex.core.dal.tablestore.model.MaizeRecord;
import com.drex.core.dal.tablestore.model.VideoViewingSession;
import com.drex.core.service.business.video.RealtimeRewardService;
import com.drex.core.service.business.video.model.FraudIndexResult;
import com.drex.core.service.business.video.worker.EventProcessingWorker;
import com.drex.core.service.business.video.worker.FraudCalculationWorker;
import com.drex.core.service.business.video.worker.RewardCalculationWorker;
import com.drex.core.service.cache.model.SessionEvent;
import com.drex.core.service.util.async.executor.Async;
import com.drex.core.service.util.async.wrapper.WorkerWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 实时奖励服务实现
 */
@Slf4j
@Service
public class RealtimeRewardServiceImpl implements RealtimeRewardService {

    @Autowired
    private MaizeRecordBuilder maizeRecordBuilder;

    @Autowired
    private EventProcessingWorker eventProcessingWorker;

    @Autowired
    private FraudCalculationWorker fraudCalculationWorker;

    @Autowired
    private RewardCalculationWorker rewardCalculationWorker;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public RealtimeProcessingResult processRealtimeEvents(String customerId, VideoViewingSession session,
                                                          List<SessionEvent> events, SocialConstant.PlatformEnum socialPlatform, SocialConstant.EventEnum socialEvent) {
        String sessionId = session.getSessionId();
        try {
            log.debug("Processing realtime events for session: {}, events: {}", session.getSessionId(), events.size());

            // 使用WorkerWrapper模式编排异步处理流程
            long startTime = System.currentTimeMillis();

            // 创建奖励计算Worker（依赖前面的结果）
            WorkerWrapper<RewardCalculationWorker.RewardCalculationParam, RewardGenerationResult> rewardWorkerWrapper =
                    new WorkerWrapper.Builder<RewardCalculationWorker.RewardCalculationParam, RewardGenerationResult>()
                            .id("rewardWorkerWrapper")
                            .worker(rewardCalculationWorker)
                            .callback(rewardCalculationWorker)
                            .param(new RewardCalculationWorker.RewardCalculationParam(
                                    sessionId, customerId, session.getVideoId(), session.getVideoUrl(), socialPlatform, socialEvent, session.getVideoDurationSeconds()))
                            .build();

            // 创建风险分数计算Worker（并行执行）
            WorkerWrapper<VideoViewingSession, FraudIndexResult> riskScoreWorkerWrapper =
                    new WorkerWrapper.Builder<VideoViewingSession, FraudIndexResult>()
                            .id("riskScoreWorkerWrapper")
                            .worker(fraudCalculationWorker)
                            .callback(fraudCalculationWorker)
                            .param(session)
                            .next(rewardWorkerWrapper)
                            .build();

            // 创建事件处理Worker
            WorkerWrapper<EventProcessingWorker.EventProcessingParam, EventProcessingWorker.EventProcessingResult> eventWorkerWrapper =
                    new WorkerWrapper.Builder<EventProcessingWorker.EventProcessingParam, EventProcessingWorker.EventProcessingResult>()
                            .worker(eventProcessingWorker)
                            .callback(eventProcessingWorker)
                            .id("eventWorkerWrapper")
                            .param(new EventProcessingWorker.EventProcessingParam(sessionId, events, session.getVideoUrl(), session.getVideoDurationSeconds()))
                            .next(riskScoreWorkerWrapper)
                            .build();


            // 开始执行并行任务
            log.debug("Starting parallel execution for session: {}", sessionId);
            Async.beginWork(3000, eventWorkerWrapper); // 3秒超时

            RealtimeProcessingResult result = new RealtimeProcessingResult(true, "Processing completed");

            RewardGenerationResult rewardResult = rewardWorkerWrapper.getWorkResult().getResult();
            EventProcessingWorker.EventProcessingResult eventProcessingResult = eventWorkerWrapper.getWorkResult().getResult();
            FraudIndexResult fraudIndexResult = riskScoreWorkerWrapper.getWorkResult().getResult();

            if (rewardResult != null && rewardResult.isSuccess()) {
                VideoReportResponse.RewardInfo rewardInfo = VideoReportResponse.RewardInfo.builder()
                        .rewardCode(rewardResult.getRewardCode())
                        .rewardAmount(rewardResult.getRewardAmount())
                        .rewardLevel(rewardResult.getRewardLevel())
                        .progress(eventProcessingResult.getCurrentProgress())
                        .rewardStatus(rewardResult.getRewardStatus())
                        .build();

                result.setRiskScore(fraudIndexResult.getFinalFraudScore());
                result.setRewardInfo(rewardInfo);
                result.setHasNewReward(true);
                log.info("Generated reward for session: {}, progress: {}, code: {}, riskScore: {}, status: {}",
                        sessionId, eventProcessingResult.getCurrentProgress(), rewardResult.getRewardCode(),
                        fraudIndexResult.getFinalFraudScore(), rewardResult.getRewardStatus());
            } else {
                // 即使没有生成新奖励，计算前一阶段的奖励领取状态
                Integer currentProgress = eventProcessingResult.getCurrentProgress();
                String previousProgress = redisTemplate.opsForValue().get(getSessionProgressKey(sessionId));
                if (!"".equals(previousProgress) && previousProgress != null && currentProgress != Integer.parseInt(previousProgress)) {
                    String rewardStatus = "";
                    // 2. 当前进度处于奖励阶段空白期currentProgress为-1，获取前一个阶段的奖励状态
                    MaizeRecord previousRecord = maizeRecordBuilder.findPreviousStageReward(
                            customerId, socialEvent.name(), session.getVideoId(), sessionId, Integer.parseInt(previousProgress));

                    if (previousRecord != null && currentProgress == -1){
                        rewardStatus = RexyConstant.MaizeStatus.EXPIRED.name();

                        // 构建RewardInfo，即使没有新奖励也要返回状态信息
                        VideoReportResponse.RewardInfo rewardInfo = VideoReportResponse.RewardInfo.builder()
                                .progress(currentProgress)
                                .rewardStatus(rewardStatus)
                                .build();

                        result.setRewardInfo(rewardInfo);
                    }
                    log.info("No new reward generated for session: {}, progress: {}, status: {}",
                            sessionId, currentProgress, rewardStatus);
                }
            }
            redisTemplate.opsForValue().set(getSessionProgressKey(sessionId), eventProcessingResult.getCurrentProgress().toString(), 1, TimeUnit.DAYS);
            log.debug("Realtime processing completed for session: {}, total cost: {}ms",
                    sessionId, System.currentTimeMillis() - startTime);

            result.setEffectiveWatchSeconds(eventProcessingResult.getEffectiveWatchSeconds());
            result.setWatchPercentage(eventProcessingResult.getWatchPercentage());
            return result;

        } catch (Exception e) {
            log.error("Failed to process realtime events for session: {}", sessionId, e);
            return new RealtimeProcessingResult(false, "Processing failed: " + e.getMessage());
        }
    }

    /**
     * 生成Redis key
     */
    private String getSessionProgressKey(String sessionId) {
        return "video_session_progress:" + sessionId;
    }
}
