package com.drex.core.service.business.video.impl;

import com.drex.core.service.business.video.model.IdleSession;
import com.drex.core.service.business.video.model.*;
import com.drex.core.service.business.video.model.RiskIndicators;
import com.drex.core.api.response.VideoIndicatorConfig;
import com.drex.core.service.cache.model.SessionEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 欺诈指标计算服务实现
 */
@Slf4j
public class FraudIndicatorsCalculator {

    /**
     * 各欺诈指标的阈值 -> 各欺诈指标的分值
     * @param allEvents 所有事件
     * @param videoIndicatorConfig 配置
     * @return 欺诈指标对象
     */
    public static RiskIndicators calculateFraudIndicators(List<SessionEvent> allEvents, VideoIndicatorConfig videoIndicatorConfig) {
        RiskIndicators riskIndicators = new RiskIndicators();
        if (CollectionUtils.isEmpty(allEvents)) {
            return riskIndicators;
        }

        // 重复事件指标
        double repeatedEvents = detectDuplicateEvents(allEvents);
        if (repeatedEvents >= videoIndicatorConfig.getRepeatedEvent().getThreshold()) {
            log.info("repeatedEvents: {}, threshold: {}", repeatedEvents, videoIndicatorConfig.getRepeatedEvent().getThreshold());
            double v = calculateLinearScoreWithMax(
                    repeatedEvents,
                    videoIndicatorConfig.getRepeatedEvent().getThreshold(),
                    videoIndicatorConfig.getRepeatedEvent().getMaxAllowed()
            );
            riskIndicators.setRepeatedEvents(v);
        }

        // 异常完成度指标
        double abnormalCompletion;
        // 前端上报时间 / 总时长 VS 后端计算实际时长 / 总时长

        // 低聚焦时长指标
        double focusDuration = analyzeFocusTime(allEvents);
        if (focusDuration <= videoIndicatorConfig.getFocus().getThreshold()) {
            log.info("focusDuration: {}, threshold: {}", focusDuration, videoIndicatorConfig.getFocus().getThreshold());
            double v = calculateLinearScoreWIthMin(
                    focusDuration,
                    videoIndicatorConfig.getFocus().getThreshold(),
                    videoIndicatorConfig.getFocus().getMinAllowed());
            riskIndicators.setLowFocusDuration(v);
        }

        // 长时间空闲指标
        double longIdleDuration = analyzeIdleTime(allEvents);
        if (longIdleDuration >= videoIndicatorConfig.getIdle().getThreshold()) {
            log.info("longIdleDuration: {}, threshold: {}", longIdleDuration, videoIndicatorConfig.getIdle().getThreshold());
            double v = calculateLinearScoreWithMax(
                    longIdleDuration,
                    videoIndicatorConfig.getIdle().getThreshold(),
                    videoIndicatorConfig.getIdle().getMaxAllowed());
            riskIndicators.setLongIdleDuration(v);
        }

        // 时间戳异常指标 阈值：5秒，计算公式：IndicatorValue = min(timeDiffSeconds / 5, 1.0)
        double maxTimeDifference = getMaxTimeDifference(allEvents);
        if (maxTimeDifference > 0) {
            double timeDiffSeconds = maxTimeDifference / 1000.0; // 转换为秒
            log.info("timeDiffSeconds: {}, maxDiffSeconds: {}", maxTimeDifference, videoIndicatorConfig.getTimestamp().getMaxDiffSeconds());
            riskIndicators.setTimestampAnomaly(Math.min(timeDiffSeconds / videoIndicatorConfig.getTimestamp().getMaxDiffSeconds(), 1.0));
        }

        // 事件顺序异常指标 直接触发：若检测到任何顺序异常，IndicatorValue = 1.0
        boolean validateEventSequence = validateEventSequence(allEvents);
        if (!validateEventSequence) {
            riskIndicators.setEventOrderAnomaly(1.0);
        }

        // 异常播放速度指标
        PlaybackRateAnalysisResult rateResult = analyzePlaybackRate(allEvents);
        if (rateResult.getAveragePlaybackRate() > videoIndicatorConfig.getPlaybackSpeed().getThreshold()) {
            log.info("playbackRate: {}, threshold: {}", rateResult.getAveragePlaybackRate(), videoIndicatorConfig.getPlaybackSpeed().getThreshold());
            double v = calculateLinearScoreWithMax(
                    rateResult.getAveragePlaybackRate(),
                    videoIndicatorConfig.getPlaybackSpeed().getThreshold(),
                    videoIndicatorConfig.getPlaybackSpeed().getMaxAllowed());
            riskIndicators.setExcessivePlaybackSpeed(v);
        }

        // 异常跳转指标
        SeekingBehaviorAnalysisResult seekResult = analyzeSeekingBehavior(allEvents);
        if (seekResult.getSeekFrequency() > videoIndicatorConfig.getAbnormalSeek().getThreshold()) {
            log.info("seekFrequency: {}, threshold: {}", seekResult.getSeekFrequency(), videoIndicatorConfig.getAbnormalSeek().getThreshold());
            double v = calculateLinearScoreWithMax(
                    seekResult.getSeekFrequency(),
                    videoIndicatorConfig.getAbnormalSeek().getThreshold(),
                    videoIndicatorConfig.getAbnormalSeek().getMaxAllowed());
            riskIndicators.setAbnormalSeek(v);
        }

        return riskIndicators;
    }

    public static int calculateEffectiveWatchTime(List<SessionEvent> events) {
        try {
            // 计算有效观看时长 = 播放时间 - 失焦时间
            int totalPlayTime = 0;
            int totalBlurTime = 0;

            // 按时间排序事件
            List<SessionEvent> sortedEvents = events.stream()
                    .sorted(Comparator.comparing(SessionEvent::getClientTimestamp))
                    .toList();

            Long lastPlayTime = null;
            Long lastFocusTime = null;
            boolean isPlaying = false;
            boolean isFocused = true;

            for (SessionEvent event : sortedEvents) {
                String eventType = event.getEventType();
                Long timestamp = event.getClientTimestamp();

                switch (eventType) {
                    case "PLAY":
                        if (!isPlaying) {
                            lastPlayTime = timestamp;
                            isPlaying = true;
                        }
                        break;
                    case "PAUSE":
                        if (isPlaying && lastPlayTime != null) {
                            totalPlayTime += (int) ((timestamp - lastPlayTime) / 1000);
                            isPlaying = false;
                        }
                        break;
                    case "BLUR":
                        if (isFocused) {
                            lastFocusTime = timestamp;
                            isFocused = false;
                        }
                        break;
                    case "FOCUS":
                        if (!isFocused && lastFocusTime != null) {
                            totalBlurTime += (int) ((timestamp - lastFocusTime) / 1000);
                            isFocused = true;
                        }
                        break;
                }
            }

            // 处理未结束的状态
            if (isPlaying && lastPlayTime != null && !sortedEvents.isEmpty()) {
                long lastEventTime = sortedEvents.get(sortedEvents.size() - 1).getClientTimestamp();
                totalPlayTime += (int) ((lastEventTime - lastPlayTime) / 1000);
            }

            if (!isFocused && lastFocusTime != null && !sortedEvents.isEmpty()) {
                long lastEventTime = sortedEvents.get(sortedEvents.size() - 1).getClientTimestamp();
                totalBlurTime += (int) ((lastEventTime - lastFocusTime) / 1000);
            }

            return Math.max(0, totalPlayTime - totalBlurTime);

        } catch (Exception e) {
            log.error("Failed to calculate effective watch time", e);
            return 0;
        }
    }

//    public CompletionPercentageAnalysisResult analyzeCompletionPercentage(List<SessionEvent> events, int videoDurationSeconds) {
//        try {
//            int totalPlaySeconds = calculateTotalPlayTime(events);
//            int effectivePlaySeconds = FraudIndicatorsCalculator.calculateEffectiveWatchTime(events);
//
//            double completionPercentage = videoDurationSeconds > 0 ?
//                    (double) totalPlaySeconds / videoDurationSeconds : 0.0;
//
//            CompletionPercentageAnalysisResult result = new CompletionPercentageAnalysisResult(
//                    completionPercentage, totalPlaySeconds, effectivePlaySeconds);
//
////            // 检测异常完成百分比
////            boolean isAbnormalCompletion = completionPercentage > FraudIndicatorThresholds.COMPLETION_PERCENTAGE_MAX_ALLOWED ||
////                    (completionPercentage > FraudIndicatorThresholds.COMPLETION_PERCENTAGE_EXPECTED &&
////                            effectivePlaySeconds < totalPlaySeconds * 0.5); // 实际观看时间远小于报告时间
////
////            result.setAbnormalCompletion(isAbnormalCompletion);
////
////            return result;
//
//        } catch (Exception e) {
//            log.error("Failed to analyze completion percentage", e);
//            return new CompletionPercentageAnalysisResult(0.0, 0, 0);
//        }
//        return null;
//    }
//
//    /**
//     * 计算总播放时间
//     */
//    private int calculateTotalPlayTime(List<SessionEvent> events) {
//        // 简化实现：基于TIMEUPDATE事件的最大时间
//        return events.stream()
//                .filter(event -> "TIMEUPDATE".equals(event.getEventType()))
//                .filter(event -> event.getEventData() != null && event.getEventData().get("currentTime") != null)
//                .mapToInt(event -> ((Number) event.getEventData().get("currentTime")).intValue())
//                .max()
//                .orElse(0);
//    }

    public static double detectDuplicateEvents(List<SessionEvent> events) {
        Map<String, Integer> eventSignatures = new HashMap<>();
        int maxFrequency = 1;

        for (SessionEvent event : events) {
            String signature = generateEventSignature(event);
            int count = eventSignatures.merge(signature, 1, Integer::sum);
            if (count > maxFrequency) {
                maxFrequency = count;
            }
        }
        return maxFrequency;
    }

    public static String generateEventSignature(SessionEvent event) {
        return String.format("%s_%s_%s_%d",
                event.getEventType(),
                event.getSessionId(),
                event.getCustomerId(),
                event.getClientTimestamp() / 1000);
    }

    /**
     * 计算线性分数，用于将指标值映射到0-1范围
     * @param value 当前值
     * @param threshold 阈值，低于此值得分为0
     * @param maxAllowed 最大值，高于此值得分为1
     * @return 0-1之间的分数
     */
    public static double calculateLinearScoreWithMax(double value, double threshold, double maxAllowed) {
        if (value <= threshold) {
            return 0.0;
        } else if (value >= maxAllowed) {
            return 1.0;
        } else {
            return (value - threshold) / (maxAllowed - threshold);
        }
    }

    /**
     * 计算线性分数，用于将指标值映射到0-1范围
     * @param value 当前值
     * @param threshold 阈值，高于此值得分为0
     * @param minAllowed 最小值，低于此值得分为1
     * @return 0-1之间的分数
     */
    public static double calculateLinearScoreWIthMin(double value, double threshold, double minAllowed) {
        if (value >= threshold) {
            return 0.0;
        } else if (value <= minAllowed) {
            return 1.0;
        } else {
            return (threshold - value) / (threshold - minAllowed);
        }
    }

    public static double getMaxTimeDifference(List<SessionEvent> events) {
        double maxTimeDifference = 0.0;
        for (SessionEvent event : events) {
            if (event.getClientTimestamp() != null && event.getServerTimestamp() != null) {
                double timeDiff = Math.abs(event.getServerTimestamp() - event.getClientTimestamp());
                maxTimeDifference = Math.max(maxTimeDifference, timeDiff);
            }
        }
        return maxTimeDifference;
    }

    public static boolean validateEventSequence(List<SessionEvent> events) {
        // 按时间戳排序事件
        List<SessionEvent> sortedEvents = events.stream()
                .sorted(Comparator.comparing(SessionEvent::getClientTimestamp))
                .toList();
        
        String previousEventType = null;

        for (SessionEvent event : sortedEvents) {
            String currentEventType = event.getEventType();
            if (previousEventType != null) {
                if (!isValidTransition(previousEventType, currentEventType)) {
                    log.warn("Invalid event transition detected: {} -> {} at timestamp {}",
                            previousEventType, 
                            currentEventType, 
                            event.getClientTimestamp());
                    return false;
                }
            }
            previousEventType = currentEventType;
        }
        return true;
    }

    /**
     * 检查事件转换是否有效
     * @param fromEvent 前一个事件类型
     * @param toEvent 当前事件类型
     * @return 转换是否有效
     */
    public static boolean isValidTransition(String fromEvent, String toEvent) {
        // 1. 相同事件的连续出现
        if (fromEvent.equals(toEvent)) {
            // 不允许连续两个相同的播放或暂停事件
            if (fromEvent.equals("PLAYING") || fromEvent.equals("PAUSED")) {
                return false;
            }
        }
        
        // 2. 特定事件转换规则
        switch (fromEvent) {
            case "PLAYING":
                // 播放状态后可以转为暂停、停止、跳转或失去焦点
                if (!Set.of("PAUSED", "SEEK", "FOCUS_LOST", "USER_STATE").contains(toEvent)) {
                    return false;
                }
                break;
                
            case "PAUSED":
                // 暂停后可以转为播放、跳转或用户状态更新
                if (!Set.of("PLAYING", "SEEK", "FOCUS_GAINED", "USER_STATE").contains(toEvent)) {
                    return false;
                }
                break;
                
            case "SEEK":
                // 跳转后应该回到播放或暂停状态
                if (!Set.of("PLAYING", "PAUSED").contains(toEvent)) {
                    return false;
                }
                break;
                
            case "FOCUS_LOST":
                // 失去焦点后可以恢复焦点或保持当前状态
                if (!Set.of("FOCUS_GAINED", "USER_STATE").contains(toEvent)) {
                    return false;
                }
                break;
                
            case "FOCUS_GAINED":
                // 获得焦点后可以开始播放或暂停
                if (!Set.of("PLAYING", "PAUSED", "USER_STATE").contains(toEvent)) {
                    return false;
                }
                break;
                
            case "USER_STATE":
                // 用户状态更新后可以转换为任何有效状态
                break;
        }
        
        return true;
    }

    public static double analyzeFocusTime(List<SessionEvent> events) {
        try {
            long totalFocusTime = 0;

            // 按时间排序事件
            List<SessionEvent> sortedEvents = events.stream()
                    .filter(event -> "FOCUS".equals(event.getEventType()) || "BLUR".equals(event.getEventType()))
                    .sorted(Comparator.comparing(SessionEvent::getClientTimestamp))
                    .collect(Collectors.toList());

            boolean currentlyFocused = true; // 假设初始状态为聚焦
            Long lastStateChangeTime = null;

            if (!sortedEvents.isEmpty()) {
                lastStateChangeTime = sortedEvents.get(0).getClientTimestamp();
            }

            for (SessionEvent event : sortedEvents) {
                long timestamp = event.getClientTimestamp();

                if (lastStateChangeTime != null) {
                    long duration = timestamp - lastStateChangeTime;

                    if (currentlyFocused) {
                        totalFocusTime += duration;
                    }
                }

                currentlyFocused = "FOCUS".equals(event.getEventType());
                lastStateChangeTime = timestamp;
            }

            // 处理最后一段时间
            if (lastStateChangeTime != null && !events.isEmpty()) {
                long lastEventTime = events.stream()
                        .mapToLong(SessionEvent::getClientTimestamp)
                        .max()
                        .orElse(lastStateChangeTime);
                long remainingDuration = lastEventTime - lastStateChangeTime;

                if (currentlyFocused) {
                    totalFocusTime += remainingDuration;
                }
            }

            long totalSessionDuration = getSessionDuration(events);
            return totalSessionDuration > 0 ?
                    (double) totalFocusTime / totalSessionDuration : 1.0;

        } catch (Exception e) {
            log.error("Failed to analyze focus time", e);
            return 1.0;
        }
    }

    public static double analyzeIdleTime(List<SessionEvent> events) {
        try {
            List<IdleSession> idleSessions = extractIdleSessions(events);
            long totalIdleTime = idleSessions.stream()
                    .mapToLong(IdleSession::getDuration)
                    .sum();
            // 计算空闲时间百分比
            long totalSessionDuration = getSessionDuration(events);
            return totalSessionDuration > 0 ? (double) totalIdleTime / totalSessionDuration : 0.0;
        } catch (Exception e) {
            log.error("Failed to analyze idle time", e);
            return 0.0;
        }
    }

    /**
     * 提取空闲会话
     */
    public static List<IdleSession> extractIdleSessions(List<SessionEvent> events) {
        List<IdleSession> idleSessions = new ArrayList<>();

        List<SessionEvent> activityEvents = events.stream()
                .filter(event -> event != null &&
                        event.getDetails() != null &&
                        event.getDetails().getUserStateData() != null &&
                        event.getClientTimestamp() != null)
                .sorted(Comparator.comparing(SessionEvent::getClientTimestamp))
                .toList();

        Long idleStartTime = null;
        for (SessionEvent event : activityEvents) {
            String activityState = event.getDetails().getUserStateData().getState();
            if (activityState == null) {
                continue;
            }

            if ("idle".equalsIgnoreCase(activityState) && idleStartTime == null) {
                idleStartTime = event.getClientTimestamp();
            } else if (!"idle".equalsIgnoreCase(activityState) && idleStartTime != null) {
                idleSessions.add(new IdleSession(idleStartTime, event.getClientTimestamp()));
                idleStartTime = null;
            }
        }

        if (idleStartTime != null) {
            idleSessions.add(new IdleSession(idleStartTime, activityEvents.get(activityEvents.size() - 1).getClientTimestamp()));
        }
        return idleSessions;
    }

    /**
     * 获取会话持续时间（毫秒）
     */
    public static long getSessionDuration(List<SessionEvent> events) {
        if (events.isEmpty()) return 0;

        long startTime = events.stream().mapToLong(SessionEvent::getClientTimestamp).min().orElse(0);
        long endTime = events.stream().mapToLong(SessionEvent::getClientTimestamp).max().orElse(0);

        return endTime - startTime;
    }

    /**
     * 获取会话持续时间（分钟）
     */
    public static long getSessionDurationMinutes(List<SessionEvent> events) {
        if (events.isEmpty()) return 1;

        long startTime = events.stream().mapToLong(SessionEvent::getClientTimestamp).min().orElse(0);
        long endTime = events.stream().mapToLong(SessionEvent::getClientTimestamp).max().orElse(0);

        return Math.max(1, (endTime - startTime) / (60 * 1000)); // 转换为分钟
    }

    public static PlaybackRateAnalysisResult analyzePlaybackRate(List<SessionEvent> events) {
        try {
            List<PlaybackRateEvent> rateEvents = extractPlaybackRateEvents(events);

            if (rateEvents.isEmpty()) {
                return new PlaybackRateAnalysisResult(1.0, 1.0, 1.0, 0);
            }

            double totalDuration = rateEvents.stream().mapToDouble(PlaybackRateEvent::getDuration).sum();
            double weightedRateSum = rateEvents.stream()
                    .mapToDouble(event -> event.getRate() * event.getDuration())
                    .sum();

            double averagePlaybackRate = totalDuration > 0 ? weightedRateSum / totalDuration : 1.0;
            double maxPlaybackRate = rateEvents.stream().mapToDouble(PlaybackRateEvent::getRate).max().orElse(1.0);
            double minPlaybackRate = rateEvents.stream().mapToDouble(PlaybackRateEvent::getRate).min().orElse(1.0);
            int rateChangeCount = rateEvents.size();

            PlaybackRateAnalysisResult result = new PlaybackRateAnalysisResult(
                    averagePlaybackRate, maxPlaybackRate, minPlaybackRate, rateChangeCount);

            // 计算异常速率百分比
            double abnormalDuration = rateEvents.stream()
                    .filter(event -> Math.abs(event.getRate() - 1.0) > 0.2)
                    .mapToDouble(PlaybackRateEvent::getDuration)
                    .sum();
            result.setAbnormalRatePercentage(totalDuration > 0 ? abnormalDuration / totalDuration : 0.0);
            result.setRateEvents(rateEvents);

            return result;
        } catch (Exception e) {
            log.error("Failed to analyze playback rate", e);
            return new PlaybackRateAnalysisResult(1.0, 1.0, 1.0, 0);
        }
    }

    /**
     * 提取播放速率事件
     */
    public static List<PlaybackRateEvent> extractPlaybackRateEvents(List<SessionEvent> events) {
        List<PlaybackRateEvent> rateEvents = new ArrayList<>();

        for (int i = 0; i < events.size(); i++) {
            SessionEvent event = events.get(i);
            if ("PLAYBACK_RATE_CHANGE".equals(event.getEventType()) && event.getEventData() != null) {
                Object rateObj = event.getEventData().get("playbackRate");
                if (rateObj instanceof Number) {
                    double rate = ((Number) rateObj).doubleValue();
                    long timestamp = event.getClientTimestamp();

                    // 计算持续时间（到下一个速率变化事件或会话结束）
                    double duration = 0.0;
                    for (int j = i + 1; j < events.size(); j++) {
                        if ("PLAYBACK_RATE_CHANGE".equals(events.get(j).getEventType())) {
                            duration = (events.get(j).getClientTimestamp() - timestamp) / 1000.0;
                            break;
                        }
                    }
                    if (duration == 0.0 && i < events.size() - 1) {
                        duration = (events.get(events.size() - 1).getClientTimestamp() - timestamp) / 1000.0;
                    }

                    rateEvents.add(new PlaybackRateEvent(timestamp, rate, duration));
                }
            }
        }

        return rateEvents;
    }

    public static SeekingBehaviorAnalysisResult analyzeSeekingBehavior(List<SessionEvent> events) {
        try {
            List<SeekEvent> seekEvents = extractSeekEvents(events);

            if (seekEvents.isEmpty()) {
                return new SeekingBehaviorAnalysisResult(0, 0.0);
            }

            int totalSeekCount = seekEvents.size();
            double averageSeekDistance = seekEvents.stream()
                    .mapToDouble(SeekEvent::getDistance)
                    .average()
                    .orElse(0.0);

            int forwardSeekCount = (int) seekEvents.stream()
                    .filter(event -> event.getToTime() > event.getFromTime())
                    .count();
            int backwardSeekCount = totalSeekCount - forwardSeekCount;

            SeekingBehaviorAnalysisResult result = new SeekingBehaviorAnalysisResult(totalSeekCount, averageSeekDistance);
            result.setForwardSeekCount(forwardSeekCount);
            result.setBackwardSeekCount(backwardSeekCount);

            // 计算跳转频率（每分钟）
            long sessionDuration = getSessionDurationMinutes(events);
            double seekFrequency = sessionDuration > 0 ? (double) totalSeekCount / sessionDuration : 0.0;
            result.setSeekFrequency(seekFrequency);
            result.setSeekEvents(seekEvents);

            return result;
        } catch (Exception e) {
            log.error("Failed to analyze seeking behavior", e);
            return new SeekingBehaviorAnalysisResult(0, 0.0);
        }
    }

    /**
     * 提取跳转事件
     */
    public static List<SeekEvent> extractSeekEvents(List<SessionEvent> events) {
        List<SeekEvent> seekEvents = new ArrayList<>();
        for (SessionEvent event : events) {
            if ("SEEK".equals(event.getEventType())
                    && event.getDetails() != null && event.getDetails().getSeekData() != null) {
                SessionEvent.SeekEventData seekData = event.getDetails().getSeekData();
                if (seekData.getPreviousTime() != null && seekData.getCurrentTime() != null) {
                    double fromTime = seekData.getPreviousTime();
                    double toTime = seekData.getCurrentTime();
                    seekEvents.add(new SeekEvent(event.getClientTimestamp(), fromTime, toTime));
                }
            }
        }
        return seekEvents;
    }






}
