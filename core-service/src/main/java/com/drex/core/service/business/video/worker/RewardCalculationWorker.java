package com.drex.core.service.business.video.worker;

import com.drex.core.api.common.RexyConstant;
import com.drex.core.api.request.GenerateMaizeRequest;
import com.drex.core.api.request.SocialConstant;
import com.drex.core.api.request.SocialEventBody;
import com.drex.core.api.response.MaizeDTO;
import com.drex.core.dal.tablestore.builder.MaizeRecordBuilder;
import com.drex.core.dal.tablestore.model.MaizeRecord;
import com.drex.core.service.business.rexy.MaizeService;
import com.drex.core.service.business.video.RealtimeRewardService;
import com.drex.core.service.business.video.model.FraudIndexResult;
import com.drex.core.service.config.VideoRiskConfigLoader;
import com.drex.core.service.config.VideoRiskProperties;
import com.drex.core.service.config.YouTubeRewardProperties;
import com.drex.core.service.util.async.callback.ICallback;
import com.drex.core.service.util.async.callback.IWorker;
import com.drex.core.service.util.async.worker.WorkResult;
import com.drex.core.service.util.async.wrapper.WorkerWrapper;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 奖励计算Worker
 * 负责生成奖励代码和积分
 */
@Slf4j
@Component
public class RewardCalculationWorker implements IWorker<RewardCalculationWorker.RewardCalculationParam, RealtimeRewardService.RewardGenerationResult>,
        ICallback<RewardCalculationWorker.RewardCalculationParam, RealtimeRewardService.RewardGenerationResult> {

    @Autowired
    private YouTubeRewardProperties youTubeRewardProperties;
    @Resource
    private MaizeService maizeService;
    @Resource
    private MaizeRecordBuilder maizeRecordBuilder;

    @Override
    public RealtimeRewardService.RewardGenerationResult action(RewardCalculationParam param, 
                                                              Map<String, WorkerWrapper> allWrappers) {
        try {
            //模拟数据测试
            if (youTubeRewardProperties.getTestSessionId().contains(param.getSessionId())) {
                // 3. 构建生成玉米请求，包含新的字段
                GenerateMaizeRequest generateRequest = GenerateMaizeRequest.builder()
                        .customerId(param.getCustomerId())
                        .socialPlatform(param.getSocialPlatform())
                        .socialEvent(param.getSocialEvent())
                        .socialEventBody(SocialEventBody.builder()
                                .socialContentId(param.getVideoId())
                                .videoDuration((long) param.getVideoDurationSeconds())
                                .watchedDuration((long) (10.0 * param.getVideoDurationSeconds()))
                                .build())
                        .progress(1)
                        .sessionId(param.getSessionId())
                        .build();

                MaizeDTO maizeDTO = maizeService.generateMaize(generateRequest);

                // 6. 构建返回结果
                RealtimeRewardService.RewardGenerationResult result = new RealtimeRewardService.RewardGenerationResult(
                        true, "Reward generated successfully");
                result.setRewardCode(maizeDTO.getCode());
                result.setRewardAmount(200L);
                result.setRewardLevel("GOLD");
                result.setRewardStatus(maizeDTO.getMaizeStatus() == RexyConstant.MaizeStatus.ISSUED ? RexyConstant.MaizeStatus.UNCLAIMED.name() : maizeDTO.getMaizeStatus().name());

                log.info("Reward generated successfully for session: {}, code: {}, score: {}, status: {}",
                        param.getSessionId(), maizeDTO.getCode(), maizeDTO.getScore(), maizeDTO.getMaizeStatus().name());

                return result;
            }

            WorkResult<EventProcessingWorker.EventProcessingResult> eventWorkerWrapper = allWrappers.get("eventWorkerWrapper").getWorkResult();
            WorkResult<FraudIndexResult> riskScoreWorkerWrapper = allWrappers.get("riskScoreWorkerWrapper").getWorkResult();
            EventProcessingWorker.EventProcessingResult eventProcessingResult = eventWorkerWrapper.getResult();
            if(riskScoreWorkerWrapper.getResult().getFinalFraudScore() >= youTubeRewardProperties.getGlobal().getRiskScoreThreshold()){
                return new RealtimeRewardService.RewardGenerationResult(false,
                        "Risk score threshold exceeded for session: " + param.getSessionId());
            }
            Integer progress = eventProcessingResult.getCurrentProgress();
            double watchPercentage = eventProcessingResult.getWatchPercentage();

            MaizeRecord receiveReward = canReceiveReward(param.getCustomerId(), param.getSessionId(), param.getVideoId(), progress, param.getSocialEvent().name());
            if(receiveReward != null) {
                RealtimeRewardService.RewardGenerationResult result = new RealtimeRewardService.RewardGenerationResult(
                        true, "Reward generated successfully");
                result.setRewardCode(receiveReward.getMaizeCode());
                result.setRewardAmount(receiveReward.getMaizeScore());
                result.setRewardLevel(receiveReward.getMaizeLevel());
                result.setRewardStatus(RexyConstant.MaizeStatus.UNCLAIMED.name());
                return result;
            }
            log.debug("Calculating reward for session: {}, progress: {}",
                    param.getSessionId(), progress);

            // 1. 获取奖励规则配置
            YouTubeRewardProperties.ProgressStage progressStage = youTubeRewardProperties.getProgressStage(
                    progress, param.getVideoUrl());
            if (progressStage == null) {
                return new RealtimeRewardService.RewardGenerationResult(false,
                        "Progress stage not found for progress: " + progress);
            }

            // 3. 构建生成玉米请求，包含新的字段
            GenerateMaizeRequest generateRequest = GenerateMaizeRequest.builder()
                    .customerId(param.getCustomerId())
                    .socialPlatform(param.getSocialPlatform())
                    .socialEvent(param.getSocialEvent())
                    .socialEventBody(SocialEventBody.builder()
                            .socialContentId(param.getVideoId())
                            .videoDuration((long) param.getVideoDurationSeconds())
                            .watchedDuration((long) (watchPercentage * param.getVideoDurationSeconds()))
                            .build())
                    .progress(progress)
                    .sessionId(param.getSessionId())
                    .build();

            MaizeDTO maizeDTO = maizeService.generateMaize(generateRequest);

            // 6. 构建返回结果
            RealtimeRewardService.RewardGenerationResult result = new RealtimeRewardService.RewardGenerationResult(
                    true, "Reward generated successfully");
            result.setRewardCode(maizeDTO.getCode());
            result.setRewardAmount(progressStage.getRewardAmount());
            result.setRewardLevel(progressStage.getRewardLevel());
            result.setRewardStatus(maizeDTO.getMaizeStatus() == RexyConstant.MaizeStatus.ISSUED ? RexyConstant.MaizeStatus.UNCLAIMED.name() : maizeDTO.getMaizeStatus().name());

            log.info("Reward generated successfully for session: {}, progress: {}, code: {}, score: {}, status: {}",
                    param.getSessionId(), progress, maizeDTO.getCode(), maizeDTO.getScore(), maizeDTO.getMaizeStatus().name());

            return result;

        } catch (Exception e) {
            log.error("Failed to generate reward for session: {}", param.getSessionId(), e);
            return new RealtimeRewardService.RewardGenerationResult(false, "Failed to generate reward: " + e.getMessage());
        }
    }

    @Override
    public void begin() {
        log.debug("RewardCalculationWorker begin");
    }

    @Override
    public void result(boolean success, RewardCalculationParam param, WorkResult<RealtimeRewardService.RewardGenerationResult> result) {
        if (success) {
            log.debug("RewardCalculationWorker completed successfully for session: {}", param.getSessionId());
        } else {
            log.error("RewardCalculationWorker failed for session: {}", param.getSessionId());
        }
    }


    /**
     * 奖励计算参数
     */
    @Data
    public static class RewardCalculationParam {
        private final String sessionId;
        private final String customerId;
        private final String videoId;
        private final String videoUrl;
        private final SocialConstant.PlatformEnum socialPlatform;
        private final SocialConstant.EventEnum socialEvent;
        private final int videoDurationSeconds;

        public RewardCalculationParam(String sessionId, String customerId, String videoId, String videoUrl,SocialConstant.PlatformEnum socialPlatform,
                                      SocialConstant.EventEnum socialEvent, Integer videoDurationSeconds) {
            this.sessionId = sessionId;
            this.customerId = customerId;
            this.videoId = videoId;
            this.videoUrl = videoUrl;
            this.socialPlatform = socialPlatform;
            this.socialEvent = socialEvent;
            this.videoDurationSeconds = videoDurationSeconds;
        }
    }

    private MaizeRecord canReceiveReward(String customerId, String sessionId, String videoId, Integer progress, String socialEvent) {
        try {
            // 使用组合键查询maizeRecord表检查是否已发放奖励
            // customerId + socialEvent + socialPlatform + socialContentId + sessionId + progress
            String maizeStatus = RexyConstant.MaizeStatus.UNCLAIMED.name();
            return maizeRecordBuilder.findMaizeRecordByCompositeKey(
                    customerId, socialEvent, videoId, sessionId, progress);

        } catch (Exception e) {
            log.error("Failed to check reward eligibility for session: {}, progress: {}", sessionId, progress, e);
            return null;
        }
    }
}
