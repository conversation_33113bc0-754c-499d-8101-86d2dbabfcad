package com.drex.core.dal.tablestore.builder;

import com.drex.core.dal.tablestore.model.MaizeRecord;
import java.util.List;

public interface MaizeRecordBuilder {

    boolean save(MaizeRecord maizeRecordDO);

    boolean updateStatus(String maizeCode, String status);

    MaizeRecord findMaizeRecord(String maizeCode);

    MaizeRecord findMaizeRecord(String customerId, String socialEvent, String socialContentId);

    List<MaizeRecord> findMaizeRecords(String customerId, String socialEvent, String socialContentId);

    /**
     * 根据复合键查询奖励记录
     * 用于检查是否已发放特定进度的奖励
     *
     * @param customerId 用户ID
     * @param socialEvent 社交事件类型（固定值"watch"）
     * @param socialContentId YouTube视频ID
     * @param sessionId 会话ID
     * @param progress 进度等级（1、2、3）
     * @return 奖励记录，如果不存在则返回null
     */
    MaizeRecord findMaizeRecordByCompositeKey(String customerId, String socialEvent,
                                            String socialContentId, String sessionId, Integer progress, String maizeStatus);

    /**
     * 查询指定进度之前的最高进度奖励记录
     * 用于获取前一个阶段的奖励状态
     *
     * @param customerId 用户ID
     * @param socialEvent 社交事件类型（固定值"watch"）
     * @param socialContentId YouTube视频ID
     * @param sessionId 会话ID
     * @param currentProgress 当前进度等级
     * @return 前一个阶段的奖励记录，如果不存在则返回null
     */
    MaizeRecord findPreviousStageReward(String customerId, String socialEvent,
                                      String socialContentId, String sessionId, Integer currentProgress);


    List<MaizeRecord> findMaizeRecordsByCustomerId(String customerId);

    List<MaizeRecord> findLastMaizeRecordsByCustomerId(String customerId);
}

