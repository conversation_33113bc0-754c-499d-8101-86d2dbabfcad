package com.drex.core.api.response;

import com.drex.core.api.common.RexyConstant;
import com.drex.core.api.request.SocialConstant;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
public class MaizeDTO implements Serializable {

    private SocialConstant.PlatformEnum socialPlatform;
    private SocialConstant.EventEnum socialEvent;

    private String code;
    private String level;
    private Integer progress;
    private Long score;
    private Boolean hidden;//true 前端不展示
    private RexyConstant.MaizeStatus maizeStatus;
    private Long expireTime;
    private List<MaizeDTO> extraRewards;
}
