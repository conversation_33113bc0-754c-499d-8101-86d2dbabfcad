# Server configuration
server.port=8080

# Dubbo configuration
dubbo.application.name=drex-core
dubbo.application.id=drex-core
dubbo.application.checkSerializable=false
dubbo.application.version=1.0.0
dubbo.protocol.server=netty
dubbo.protocol.name=dubbo
dubbo.protocol.port=20880
dubbo.protocol.threadpool=fixed
dubbo.protocol.threads=50
dubbo.protocol.queues=1000
dubbo.provider.timeout=10000
dubbo.consumer.timeout=10000
dubbo.registry.address=zookeeper://zookeeper.drex-dev.svc.cluster.local:2181
dubbo.consumer.group=kktd
dubbo.provider.group=kktd
dubbo.application.parameters.router=traffic
dubbo.provider.parameters.traffic=${TRAFFIC_TAG}
dubbo.provider.prefer-serialization=hessian2,fastjson2
dubbo.provider.serialization=hessian2
dubbo.application.serialize-check-status=WARN
dubbo.application.check-serializable=false
dubbo.application.metadata-type=remote
dubbo.tracing.enabled=true

management.metrics.tags.application=drex-core
management.health.db.enabled=true
management.health.solr.enabled=false
management.health.mongo.enabled=true
management.health.cassandra.enabled=false
management.health.elasticsearch.enabled=false
management.health.influxdb.enabled=false
management.health.neo4j.enabled=false
management.server.port=9090
management.health.defaults.enabled=false
management.endpoint.health.show-details=always
management.endpoints.migrate-legacy-ids=true
management.endpoint.metrics.enabled=true
management.endpoint.prometheus.enabled=true
management.prometheus.metrics.export.enabled=true
dubbo.metrics.protocol=prometheus
dubbo.metrics.aggregation.enabled=true
dubbo.metrics.prometheus.exporter.enabled=true
management.endpoints.web.exposure.include=env,health,info,httptrace,metrics,heapdump,threaddump,prometheus,dubbo,druid

# Logging configuration
logging.level.root=INFO
logging.pattern.console=[%p][%t][%d{yyyy-MM-dd HH:mm:ss.SSS}][%c][%L][%X{traceId}][%X{spanId}]%m%n

kiki.ots.endpoint=https://DrexDev.ap-southeast-1.ots.aliyuncs.com
kiki.ots.instanceName=DrexDev

# YouTube????????
youtube.cache.session.expire-seconds=86400
youtube.cache.session-key.expire-seconds=86400
youtube.cache.event.expire-seconds=86400

# YouTube????????4????
youtube.reward.long-video.video-type=LONG
youtube.reward.long-video.enabled=true
youtube.reward.long-video.duration-threshold-seconds=300

# ?????1 (20%-40%?????)
youtube.reward.long-video.stages[0].stage=1
youtube.reward.long-video.stages[0].stage-name=????
youtube.reward.long-video.stages[0].min-watch-percentage=0.2
youtube.reward.long-video.stages[0].max-watch-percentage=0.4
youtube.reward.long-video.stages[0].reward-amount=25
youtube.reward.long-video.stages[0].reward-level=BRONZE
youtube.reward.long-video.stages[0].enabled=true

# ?????2 (50%-70%?????)
youtube.reward.long-video.stages[1].stage=2
youtube.reward.long-video.stages[1].stage-name=????
youtube.reward.long-video.stages[1].min-watch-percentage=0.5
youtube.reward.long-video.stages[1].max-watch-percentage=0.7
youtube.reward.long-video.stages[1].reward-amount=25
youtube.reward.long-video.stages[1].reward-level=SILVER
youtube.reward.long-video.stages[1].enabled=true

# ?????3 (80%-100%?????)
youtube.reward.long-video.stages[2].stage=3
youtube.reward.long-video.stages[2].stage-name=????
youtube.reward.long-video.stages[2].min-watch-percentage=0.8
youtube.reward.long-video.stages[2].max-watch-percentage=1.2
youtube.reward.long-video.stages[2].reward-amount=25
youtube.reward.long-video.stages[2].reward-level=GOLD
youtube.reward.long-video.stages[2].enabled=true

# YouTube????????1????
youtube.reward.short-video.video-type=SHORT
youtube.reward.short-video.enabled=true
youtube.reward.short-video.duration-threshold-seconds=300

# ?????1 (80%???????)
youtube.reward.short-video.stages[0].stage=1
youtube.reward.short-video.stages[0].stage-name=????
youtube.reward.short-video.stages[0].min-watch-percentage=0.8
youtube.reward.short-video.stages[0].max-watch-percentage=1.2
youtube.reward.short-video.stages[0].reward-amount=20
youtube.reward.short-video.stages[0].reward-level=GOLD
youtube.reward.short-video.stages[0].enabled=true

# X????????
youtube.reward.x-platform.enabled=true

# X??????
youtube.reward.x-platform.post-reward.reward-amount=15
youtube.reward.x-platform.post-reward.reward-level=GOLD
youtube.reward.x-platform.post-reward.enabled=true
youtube.reward.x-platform.post-reward.max-daily-rewards=5

# X??????
youtube.reward.x-platform.reply-reward.reward-amount=10
youtube.reward.x-platform.reply-reward.reward-level=SILVER
youtube.reward.x-platform.reply-reward.enabled=true
youtube.reward.x-platform.reply-reward.max-daily-rewards=10

# YouTube????
youtube.reward.global.min-session-duration-seconds=60
youtube.reward.global.max-session-duration-seconds=7200
youtube.reward.global.enable-realtime-reward=true
youtube.reward.global.heartbeat-interval-seconds=30
youtube.reward.global.event-report-interval-seconds=10
youtube.reward.global.max-concurrent-sessions=5
youtube.reward.global.short-video-threshold-seconds=300

# ??????????????????????????
youtube.reward.global.risk-score-threshold=95



# AsyncTool?????
async.event-processing.core-pool-size=4
async.event-processing.max-pool-size=16
async.event-processing.queue-capacity=1000
async.event-processing.keep-alive-seconds=60

async.reward-calculation.core-pool-size=2
async.reward-calculation.max-pool-size=8
async.reward-calculation.queue-capacity=500
async.reward-calculation.keep-alive-seconds=60

async.fraud-detection.core-pool-size=2
async.fraud-detection.max-pool-size=6
async.fraud-detection.queue-capacity=300
async.fraud-detection.keep-alive-seconds=60


youtube.reward.test-session-id=1939965148366499840,1938115963736231936